pluginManagement {
    repositories {
        // 自定义本地仓库（优先）
        maven {
            url=uri("file:/Users/<USER>/bin/maven/mylib")
        }
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        // 自定义本地仓库（优先）
        maven {
            url=uri("file:/Users/<USER>/bin/maven/mylib")
        }
        google()
        mavenCentral()
    }
    versionCatalogs {
        create("libs") {
            from(files("./libs.versions.toml"))
        }
    }
}

rootProject.name = "sync"
include(":app")
 