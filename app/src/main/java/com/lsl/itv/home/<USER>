package com.lsl.itv.home

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.ui.PlayerView
import androidx.tv.material3.*
import com.lsl.itv.data.ApiResult
import com.lsl.itv.ui.theme.SyncTheme
import kotlinx.coroutines.launch



class MainActivity : ComponentActivity() {
    @UnstableApi
    @OptIn(ExperimentalTvMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SyncTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    shape = RectangleShape
                ) {
                    PlaylistScreen()
                }
            }
        }
    }
}

@UnstableApi
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlaylistScreen() {
    // 视频仓库

    // 视频列表状态
    var videoListState by remember { mutableStateOf(listOf<VideoItem>()) }
    var selectedVideo by remember { mutableStateOf<VideoItem?>(null) }

    // 协程作用域
    val coroutineScope = rememberCoroutineScope()

    val context = LocalContext.current

    // 加载视频列表
    LaunchedEffect(Unit) {
          getData()
    }

    suspend fun getData(){
        when (val result = HttpUtil.postJson<List<VideoItem>>("/user","")) {
            is ApiResult.Success -> videoListState = result.data
            is ApiResult.Error -> ""
        }
    }


    Row(
        modifier = Modifier.fillMaxSize()
    ) {
        // 左侧播放列表
        PlaylistPanel(
            videoListState = videoListState,
            selectedVideo = selectedVideo,
            onVideoSelected = { selectedVideo = it },
            onRefresh = {
                coroutineScope.launch {
                    repository.refreshVideoList().collect { state ->
                        videoListState = state
                    }
                }
            },
            modifier = Modifier
                .weight(0.4f)
                .fillMaxHeight()
        )

        // 右侧播放器
        PlayerPanel(
            selectedVideo = selectedVideo,
            modifier = Modifier
                .weight(0.6f)
                .fillMaxHeight()
        )
    }
}


@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlaylistPanel(
    videoListState: LoadingState<List<VideoItem>>,
    selectedVideo: VideoItem?,
    onVideoSelected: (VideoItem) -> Unit,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(8.dp),
        shape = CardDefaults.shape(RoundedCornerShape(12.dp)),
        onClick = {},
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "播放列表",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )

                // 刷新按钮
                Button(
                    onClick = onRefresh,
                    modifier = Modifier.size(40.dp)
                ) {
                    Text("⟳")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 根据加载状态显示不同内容
            when (videoListState) {
                is LoadingState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "加载中...",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }

                is LoadingState.Success -> {
                    val videos = videoListState.data
                    if (videos.isEmpty()) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "暂无视频",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    } else {
                        LazyColumn(
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(videos) { video ->
                                VideoListItem(
                                    video = video,
                                    isSelected = video.id == selectedVideo?.id,
                                    onClick = { onVideoSelected(video) }
                                )
                            }
                        }
                    }
                }

                is LoadingState.Error -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "加载失败",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = videoListState.exception.message ?: "未知错误",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(onClick = onRefresh) {
                                Text("重试")
                            }
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun VideoListItem(
    video: VideoItem,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.colors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        ),
        shape = CardDefaults.shape(RoundedCornerShape(8.dp)),
        onClick = {},
        onLongClick = {},
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Text(
                text = video.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = video.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "时长: ${video.duration}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

// ExoPlayer Composable
@UnstableApi
@Composable
fun VideoPlayer(
    videoUrl: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    // 使用remember创建ExoPlayer实例
    val exoPlayer = remember {
        // 创建HTTP数据源工厂
        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("ExoPlayerDemo")
            .setConnectTimeoutMs(30000)
            .setReadTimeoutMs(30000)
            .setAllowCrossProtocolRedirects(true)

        // 创建媒体源工厂
        val mediaSourceFactory = DefaultMediaSourceFactory(context)
            .setDataSourceFactory(httpDataSourceFactory)

        // 创建ExoPlayer实例
        ExoPlayer.Builder(context)
            .setMediaSourceFactory(mediaSourceFactory)
            .build()
    }

    // 处理视频URL变化
    LaunchedEffect(videoUrl) {
        try {
            val mediaItem = MediaItem.fromUri(videoUrl)
            exoPlayer.setMediaItem(mediaItem)
            exoPlayer.prepare()
            exoPlayer.playWhenReady = true
        } catch (e: Exception) {
            // 处理加载错误
            e.printStackTrace()
        }
    }

    // 管理生命周期事件
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    exoPlayer.pause()
                }
                Lifecycle.Event.ON_RESUME -> {
                    // 不自动播放，由用户控制
                }
                Lifecycle.Event.ON_DESTROY -> {
                    exoPlayer.stop()
                    exoPlayer.release()
                }
                else -> {}
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            exoPlayer.stop()
            exoPlayer.release()
        }
    }

    AndroidView(
        factory = { context ->
            PlayerView(context).apply {
                player = exoPlayer
                useController = true
                setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            }
        },
        modifier = modifier
    )
}

@UnstableApi
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlayerPanel(
    selectedVideo: VideoItem?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(start = 16.dp),
        shape = CardDefaults.shape(RoundedCornerShape(12.dp)),
        onClick = {},
        onLongClick = {},
    ) {
        if (selectedVideo != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Text(
                    text = "正在播放",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Media3 ExoPlayer 播放器
                VideoPlayer(
                    videoUrl = selectedVideo.videoUrl,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .clip(RoundedCornerShape(8.dp))
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 视频信息
                Column {
                    Text(
                        text = selectedVideo.title,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = selectedVideo.description,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "时长: ${selectedVideo.duration}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "请选择要播放的视频",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@UnstableApi
@Preview(showBackground = true)
@Composable
fun PlaylistScreenPreview() {
    SyncTheme {
        PlaylistScreen()
    }
}