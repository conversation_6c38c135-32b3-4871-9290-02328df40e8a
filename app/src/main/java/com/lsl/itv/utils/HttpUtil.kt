import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.lsl.itv.data.ApiResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.lang.reflect.Type
import java.util.concurrent.TimeUnit

object HttpUtil {
    val gson = Gson()

    val client = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(15, TimeUnit.SECONDS)
        .build()

    inline fun <reified T> parseJson(json: String): T {
        val type: Type = object : TypeToken<T>() {}.type
        return gson.fromJson(json, type)
    }

    suspend inline fun <reified T> get(
        url: String,
        headers: Map<String, String>? = null
    ): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val requestBuilder = Request.Builder().url(url)
            headers?.forEach { (k, v) -> requestBuilder.addHeader(k, v) }

            val response = client.newCall(requestBuilder.build()).execute()
            val json = response.body?.string()

            if (!response.isSuccessful) {
                return@withContext ApiResult.Error("HTTP ${response.code}: ${response.message}", response.code)
            }
            if (json == null) return@withContext ApiResult.Error("Empty response body")

            val data = parseJson<T>(json)
            ApiResult.Success(data)
        } catch (e: Exception) {
            ApiResult.Error("Network error: ${e.message}")
        }
    }

    suspend inline fun <reified T> postJson(
        url: String,
        jsonBody: String,
        headers: Map<String, String>? = null
    ): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val mediaType = "application/json; charset=utf-8".toMediaType()
            val body = jsonBody.toRequestBody(mediaType)

            val requestBuilder = Request.Builder()
                .url(url)
                .post(body)
            headers?.forEach { (k, v) -> requestBuilder.addHeader(k, v) }

            val response = client.newCall(requestBuilder.build()).execute()
            val json = response.body?.string()

            if (!response.isSuccessful) {
                return@withContext ApiResult.Error("HTTP ${response.code}: ${response.message}", response.code)
            }
            if (json == null) return@withContext ApiResult.Error("Empty response body")

            val data = parseJson<T>(json)
            ApiResult.Success(data)
        } catch (e: Exception) {
            ApiResult.Error("Network error: ${e.message}")
        }
    }

    suspend inline fun <reified T> fetch(
        url: String,
        json: String,
        headers: Map<String, String>? = null
    ): T = withContext(Dispatchers.IO) {
        when (val result = postJson<T>(url, json, headers)) {
            is ApiResult.Success -> result.data
            is ApiResult.Error -> throw RuntimeException("请求失败：${result.message} (code=${result.code})")
        }
    }
}
