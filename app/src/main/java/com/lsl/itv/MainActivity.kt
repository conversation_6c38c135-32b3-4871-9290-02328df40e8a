package com.lsl.itv

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.tv.material3.*
import com.lsl.itv.ui.theme.SyncTheme

// 数据模型
data class VideoItem(
    val id: String,
    val title: String,
    val description: String,
    val duration: String,
    val thumbnailUrl: String? = null
)

class MainActivity : ComponentActivity() {
    @OptIn(ExperimentalTvMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SyncTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    shape = RectangleShape
                ) {
                    PlaylistScreen()
                }
            }
        }
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlaylistScreen() {
    // 示例数据
    val sampleVideos = remember {
        listOf(
            VideoItem("1", "电影 1", "精彩的动作电影", "2:15:30"),
            VideoItem("2", "电影 2", "浪漫爱情故事", "1:45:20"),
            VideoItem("3", "电影 3", "科幻冒险大片", "2:30:15"),
            VideoItem("4", "电视剧 S01E01", "热门电视剧第一集", "45:30"),
            VideoItem("5", "电视剧 S01E02", "热门电视剧第二集", "46:15"),
            VideoItem("6", "纪录片", "自然世界探索", "1:20:45"),
            VideoItem("7", "音乐会", "古典音乐演出", "1:55:30"),
            VideoItem("8", "体育赛事", "足球比赛精彩回放", "2:05:20")
        )
    }

    var selectedVideo by remember { mutableStateOf(sampleVideos.firstOrNull()) }

    Row(
        modifier = Modifier.fillMaxSize()
    ) {
        // 左侧播放列表
        PlaylistPanel(
            videos = sampleVideos,
            selectedVideo = selectedVideo,
            onVideoSelected = { selectedVideo = it },
            modifier = Modifier
                .weight(0.4f)
                .fillMaxHeight()
        )

        // 右侧播放器
        PlayerPanel(
            selectedVideo = selectedVideo,
            modifier = Modifier
                .weight(0.6f)
                .fillMaxHeight()
        )
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlaylistPanel(
    videos: List<VideoItem>,
    selectedVideo: VideoItem?,
    onVideoSelected: (VideoItem) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(0.dp),
        shape = CardDefaults.shape(RoundedCornerShape(12.dp)),
        onClick = {  },
        onLongClick = {},
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(0.dp)
        ) {
            Text(
                text = "播放列表",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(videos) { video ->
                    VideoListItem(
                        video = video,
                        isSelected = video.id == selectedVideo?.id,
                        onClick = { onVideoSelected(video) }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun VideoListItem(
    video: VideoItem,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.colors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        ),
        shape = CardDefaults.shape(RoundedCornerShape(8.dp)),
        onClick = {},
        onLongClick = {},
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Text(
                text = video.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = video.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "时长: ${video.duration}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlayerPanel(
    selectedVideo: VideoItem?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(0.dp),
        shape = CardDefaults.shape(RoundedCornerShape(12.dp)),
        onClick = {},
        onLongClick = {},
    ) {
        if (selectedVideo != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Text(
                    text = "正在播放",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // 播放器区域 (模拟)
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .background(
                            Color.Black,
                            RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "▶",
                            fontSize = 48.sp,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "播放器区域",
                            color = Color.White,
                            style = MaterialTheme.typography.titleLarge
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 视频信息
                Column {
                    Text(
                        text = selectedVideo.title,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = selectedVideo.description,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "时长: ${selectedVideo.duration}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 控制按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Button(
                        onClick = { /* 播放/暂停 */ },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("播放/暂停")
                    }

                    Button(
                        onClick = { /* 停止 */ },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("停止")
                    }
                }
            }
        } else {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "请选择要播放的视频",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PlaylistScreenPreview() {
    SyncTheme {
        PlaylistScreen()
    }
}