package com.lsl.itv

import android.content.Context
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.ui.PlayerView
import androidx.tv.material3.*
import com.lsl.itv.ui.theme.SyncTheme

// 数据模型
data class VideoItem(
    val id: String,
    val title: String,
    val description: String,
    val duration: String,
    val videoUrl: String,
    val thumbnailUrl: String? = null
)

class MainActivity : ComponentActivity() {
    @UnstableApi
    @OptIn(ExperimentalTvMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SyncTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    shape = RectangleShape
                ) {
                    PlaylistScreen()
                }
            }
        }
    }
}

@UnstableApi
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlaylistScreen() {
    // 示例数据
    val sampleVideos = remember {
        listOf(
            VideoItem("1", "Big Buck Bunny", "精彩的动画短片", "10:34", "https://vjs.zencdn.net/v/oceans.mp4"),
            VideoItem("2", "Elephant Dream", "开源动画电影", "10:53", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"),
            VideoItem("3", "电影 3", "科幻冒险大片", "2:30:15", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4"),
            VideoItem("4", "电视剧 S01E01", "热门电视剧第一集", "45:30", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4"),
            VideoItem("5", "电视剧 S01E02", "热门电视剧第二集", "46:15", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4"),
            VideoItem("6", "纪录片", "自然世界探索", "1:20:45", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4"),
            VideoItem("7", "Sintel", "开源动画电影", "14:48", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4"),
            VideoItem("8", "Tears of Steel", "科幻动作短片", "12:14", "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4")
        )
    }

    var selectedVideo by remember { mutableStateOf(sampleVideos.firstOrNull())}

    Row(
        modifier = Modifier.fillMaxSize()
    ) {
        // 左侧播放列表
        PlaylistPanel(
            videos = sampleVideos,
            selectedVideo = selectedVideo,
            onVideoSelected = { selectedVideo = it },
            modifier = Modifier
                .weight(0.4f)
                .fillMaxHeight()
        )

        // 右侧播放器
        PlayerPanel(
            selectedVideo = selectedVideo,
            modifier = Modifier
                .weight(0.6f)
                .fillMaxHeight()
        )
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlaylistPanel(
    videos: List<VideoItem>,
    selectedVideo: VideoItem?,
    onVideoSelected: (VideoItem) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(0.dp),
        shape = CardDefaults.shape(RoundedCornerShape(12.dp)),
        onClick = {  },
        onLongClick = {},
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(0.dp)
        ) {
            Text(
                text = "播放列表",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(videos) { video ->
                    VideoListItem(
                        video = video,
                        isSelected = video.id == selectedVideo?.id,
                        onClick = { onVideoSelected(video) }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun VideoListItem(
    video: VideoItem,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.colors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        ),
        shape = CardDefaults.shape(RoundedCornerShape(8.dp)),
        onClick = {},
        onLongClick = {},
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Text(
                text = video.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = video.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "时长: ${video.duration}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

// ExoPlayer Composable
@UnstableApi
@Composable
fun VideoPlayer(
    videoUrl: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    // 使用remember创建ExoPlayer实例
    val exoPlayer = remember {
        // 创建HTTP数据源工厂
        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("ExoPlayerDemo")
            .setConnectTimeoutMs(30000)
            .setReadTimeoutMs(30000)
            .setAllowCrossProtocolRedirects(true)

        // 创建媒体源工厂
        val mediaSourceFactory = DefaultMediaSourceFactory(context)
            .setDataSourceFactory(httpDataSourceFactory)

        // 创建ExoPlayer实例
        ExoPlayer.Builder(context)
            .setMediaSourceFactory(mediaSourceFactory)
            .build()
    }

    // 处理视频URL变化
    LaunchedEffect(videoUrl) {
        try {
            val mediaItem = MediaItem.fromUri(videoUrl)
            exoPlayer.setMediaItem(mediaItem)
            exoPlayer.prepare()
            exoPlayer.playWhenReady = true
        } catch (e: Exception) {
            // 处理加载错误
            e.printStackTrace()
        }
    }

    // 管理生命周期事件
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    exoPlayer.pause()
                }
                Lifecycle.Event.ON_RESUME -> {
                    // 不自动播放，由用户控制
                }
                Lifecycle.Event.ON_DESTROY -> {
                    exoPlayer.stop()
                    exoPlayer.release()
                }
                else -> {}
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            exoPlayer.stop()
            exoPlayer.release()
        }
    }

    AndroidView(
        factory = { context ->
            PlayerView(context).apply {
                player = exoPlayer
                useController = true
                setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            }
        },
        modifier = modifier
    )
}

@UnstableApi
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun PlayerPanel(
    selectedVideo: VideoItem?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(start = 16.dp),
        shape = CardDefaults.shape(RoundedCornerShape(12.dp)),
        onClick = {},
        onLongClick = {},
    ) {
        if (selectedVideo != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Text(
                    text = "正在播放",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Media3 ExoPlayer 播放器
                VideoPlayer(
                    videoUrl = selectedVideo.videoUrl,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .clip(RoundedCornerShape(8.dp))
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 视频信息
                Column {
                    Text(
                        text = selectedVideo.title,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = selectedVideo.description,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "时长: ${selectedVideo.duration}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "请选择要播放的视频",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@UnstableApi
@Preview(showBackground = true)
@Composable
fun PlaylistScreenPreview() {
    SyncTheme {
        PlaylistScreen()
    }
}